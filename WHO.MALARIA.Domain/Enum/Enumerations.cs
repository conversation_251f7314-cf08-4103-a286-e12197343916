﻿using WHO.MALARIA.Domain.CustomAttribute;

namespace WHO.MALARIA.Domain.Enum
{
    /// <summary>
    /// User Status within at application level
    /// </summary>
    public enum UserStatus
    {
        /// <summary>
        /// If user has been marked in active by SuperManager and Manager or WHO
        /// </summary>
        InActive = 0,

        /// <summary>
        /// When user accepts the invitation and log in for the first time , the status changes from InvitationSent to Active
        /// </summary>
        Active = 1,

        /// <summary>
        /// When an invitation is sent to User
        /// </summary>
        InvitationSent = 2,

        /// <summary>
        /// When user registers themselves, the default status would 'Pending'
        /// </summary>
        Pending = 3,
    }

    /// <summary>
    /// Represents user role
    /// </summary>
    public enum UserRoleEnum
    {
        Viewer = 1,
        Manager = 2,
        SuperManager = 3,
        WHOAdmin = 4
    }

    /// <summary>
    /// Represents global dashboard graph type
    /// </summary>
    public enum GlobalDashboardGraphType
    {
        IndicatorDashboard = 1,
        WhoRegionalSummary = 2,
        ObjectiveMap = 3
    }

    /// <summary>
    /// Represents user identity status
    /// </summary>
    public enum IdentityStatus
    {
        Active = 1,
        InActive = 0,
    }

    /// <summary>
    /// Represents user country acess rights status
    /// </summary>
    public enum UserCountryAccessRightsEnum
    {
        Pending = 0,
        Accepted = 1,
        Rejected = 2,
        InActive = 3,
        InvitationNotAccepted = 4,
        ForwardToWHOAdmin = 5
    }

    /// <summary>
    /// Represents status of assessment as assessment progresses
    /// </summary>
    public enum AssessmentStatus
    {
        /// <summary>
        /// Assessment is created
        /// </summary>
        Created = 1,

        /// <summary>
        /// Srategies are selected
        /// </summary>
        StrategySelected = 2,

        /// <summary>
        /// Assessment type and indicators are selected
        /// </summary>
        TypeSelected = 3,

        /// <summary>
        /// Scope definition is completed and finalized and the assessment is ready
        /// for data collection
        /// </summary>
        Finalized = 4,

        /// <summary>
        /// Data collection is in progress
        /// </summary>
        InProgress = 5,

        // <summary>
        /// Assessment is completed with data collection
        /// and published
        /// </summary>
        Published = 6
    }

    /// <summary>
    /// Enum for assessment user role
    /// </summary>
    public enum AssessmentUserRole
    {
        Manager = 1,
        Editor = 2,
        Reviewer = 3,
    }

    /// <summary>
    /// Enum for Data Collection And Review Method Type
    /// </summary>
    public enum DataCollectionAndReviewMethodType
    {
        DQA = 1
    }

    /// <summary>
    /// Enum for priority (used for Indicator and Variables)
    /// </summary>
    public enum Priority
    {
        Priority = 1,
        Optional = 2,
    }

    /// <summary>
    /// Enum for strategy type
    /// </summary>
    public enum StrategyType
    {
        CaseSurveillance = 1,
        MalariaControl = 2,
    }

    /// <summary>
    /// Enum for assessment approach
    /// </summary>
    public enum AssessmentApproach
    {
        Rapid = 1,
        Tailored = 2,
        Comprehensive = 3
    }

    /// <summary>
    /// Enum for met not met status of indicator
    /// </summary>
    public enum MetNotMetStatus
    {
        [TranslationKey("Common.Met")]
        Met = 2,
        [TranslationKey("Common.PartiallyMet")]
        PartiallyMet = 1,
        [TranslationKey("Common.NotMet")]
        NotMet = 0,
        [TranslationKey("Common.NotAssessed")]
        NotAssessed = 3
    }

    /// <summary>
    /// Enum for sorting data using field direction i.e Ascending or Descending
    /// </summary>
    public enum SortDirection
    {
        //Value indicating the sorting order as Ascending
        Asc = 1,

        //Value indicating the sorting order as Descending
        Desc = 2
    }

    /// <summary>
    /// User existstance in Azure directory status
    /// </summary>
    public enum UserExistsInAzureDirectoryStatus
    {
        Exists = 1,
        DoesNotExists = 2,
        InsufficientPrivileges = 3,
        InSufficientPrivilegesToAddExistingWHOUser = 4
    }

    /// <summary>
    /// Send invitation email to user status
    /// </summary>
    public enum SendInvitationStatus
    {
        InvitedSuccesfully = 1,
        InsufficientPrivileges = 2,
        BadRequest = 3
    }

    /// <summary>
    /// Specifies the email template type
    /// </summary>
    public enum EmailTemplateType
    {
        UserActivation = 1,
        UserActivationRequestRejection = 2,
        ActivateViewerUser = 3,
        DeactivateViewerUser = 4,
        DeactivatedUserForAllAssignedCountries = 5,
        UserAssignedToAssessment = 6,
        UserRemovedFromAssessment = 7,
        UserCountryAccessGranted = 8,
        SuperManagerRoleAssignment = 9,
        WHOAdminRoleAssignment = 10,
        AccessRequestForCountryFromWHOUser = 11
    }

    /// <summary>
    /// Defines user permission on assessment
    /// </summary>
    public enum UserAssessmentPermission
    {
        CanViewDetails = 1,
        CanConfigure = 2,
        CanChangeManager = 3,
        CanChangeCountry = 4,
        CanUpdateStrategies = 5,
        CanUpdateIndicators = 6,
        CanFinalize = 7,
        CanCollectData = 8,
        CanPublish = 9,
        CanUploadFile = 10,
        CanCreateOrUpdateServiceLevel = 11,
        CanDeleteServiceLevel = 12,
        CanFinalizeServiceLevel = 13,
        CanSaveOrFinalizeDRIndicatorResponse = 14,
        CanCreateOrUpdateQuestionBank = 15,
        CanFinalizeQuestionBank = 16,
        CanEditQuestionBankRespondentType = 17,
        CanSaveOrFinalizeDeskLevelDQAVariables = 18,
        CanGenerateOrUploadDeskLevelDQATemplate = 19,
        CanEditAndSaveDeskLevelDQASummaryResult = 20,
        CanFinalizeDeskLevelDQASummaryResult = 21,
        CanExportDeskLevelDQASummary = 22,
        CanGenerateQuestionBank = 23,
        CanExportShellTable = 24,
        CanExportScoreCard = 25,
        ShowResultsOnGlobalDashboard = 26,
        IsAssessmentPublished = 27,
        CanSaveScoreCard = 28,
        CanFinalizeScoreCard = 29
    }

    /// <summary>
    /// Represents exception code of sql exception
    /// </summary>
    public enum SqlExceptionCode
    {
        IncorrectSyntax = 102,
        OperandTypeClash = 206,
        InvalidColumnName = 207,
        NotNullViolation = 515,
        ConversionFailed = 518,
        IdentityInsertOff = 544,
        ConstraintViolation = 547,
        ColumnSizeViolation = 611,
        IndexChangeViolation = 1930,
        IndexOffViolation = 1935,
        UniqueKeyConstraintViolation = 2627,
        DuplicateColumn = 2705,
        DuplicateDatabaseObject = 2714,
        IndexNotFound = 2727,
        MergeStatementConflict = 5318,
        IdentityInsertOn = 8107,
        ColumnDropConflict = 21265,
        DeleteConflict = 34012,
        RequiredFieldNotSupplied = 8178,
        StringOrBinaryDataTruncated = 8152,
        StringOrBinaryDataTruncatedUpdatedId = 2628,
        EnteredValuesLengthExceedThanColumnLength = 248,
        SequenceReachedItsMaximumValue = 11728,
        AlterSequenceFailed = 11715,
        UserAccessDenied = 297,
        MultiPartIdentifierNotIdentified = 4104
    }

    /// <summary>
    /// Represents variable type of DQA variable
    /// </summary>
    public enum DQAVariableType
    {
        ServiceLevel = 1,
        DeskLevel = 2,
        Both = 3
    }

    /// <summary>
    /// Represents register type of DQA
    /// </summary>
    public enum DQASLRegisterType
    {
        InpatientRegister = 1,
        OutpatientRegister = 2,
        LabRegister = 3
    }

    /// <summary>
    /// Represents DQA Desk Level summary result type
    /// </summary>
    public enum DQADLSummaryResultType
    {
        NationalLevelResults = 1,
        NationalLevelTarget = 2
    }

    /// <summary>
    /// Represents DQA types
    /// </summary>
    public enum DQAType
    {
        DeskLevel = 1,
        ServiceDeliveryLevel = 2
    }

    public enum QBResponseType
    {
        LetterOrderedOptions,
        NumericOrderedOptions,
        Integer,
        Text,
        Date
    }

    /// <summary>
    /// Represents case strategy type for selection
    /// </summary>
    public enum CaseStrategySelectionType
    {
        BurdenReduction = 1,
        Elimination = 2,
        Both = 3
    }

    /// <summary>
    /// Represents respondent type for question bank
    /// </summary>
    public enum QBRespondentType
    {
        [TranslationKey("QuestionBank.SubnationalLevel")]
        SubnationalLevel = 1,
        [TranslationKey("QuestionBank.ServiceDeliveryLevel")]
        ServiceDeliveryLevel = 2,
        [TranslationKey("QuestionBank.CommunityLevel")]
        CommunityLevel = 3
    }

    /// <summary>
    /// Represents desk review response status for assessments
    /// </summary>
    public enum DeskReviewAssessmentResponseStatus
    {
        InProgress = 1,
        Completed = 2
    }

    /// <summary>
    /// Represents data system type for service level DQA
    /// </summary>
    public enum DQADataSystem
    {
        System1 = 1,
        System2 = 2
    }

    public enum AuditType
    {
        None = 0,
        Create = 1,
        Update = 2,
        Delete = 3
    }

    /// <summary>
    /// Represents user language which is selected
    /// </summary>
    public enum UserLanguage
    {
        en = 0,
        fr = 1
    }

    /// <summary>
    /// Represents the categories of desk review variables
    /// </summary>
    public enum DRVariableCaseCategory
    {
        PVivax = 1,
        MalariaInpatient = 2
    }

    /// <summary>
    /// Report Response Type
    /// </summary>
    public enum ReportResponseType
    {
        DataTable = 1, // Return datatable from response when only single table
        DataSet = 2,  // Return dataset from response when multiple multiple tables
        GraphWithTab = 3 // Return tab based graph screen response        
    }

    /// <summary>
    /// Represents analytical output view type
    /// </summary>
    public enum AnalyticalOutputType
    {
        Table = 1,
        TabWithTable = 2,
        Graph = 3,
        TabWithGraph = 4,
        MultiLineGraph = 5,
        MultipleTables = 6,
        Diagram = 7,
        InteractiveTable = 8,
    }

    /// <summary>
    /// Represents chart type
    /// </summary>
    public enum GraphType
    {
        Line = 1, // Line graph chart
        Bar = 2,  // Bar graph chart       
    }

    /// <summary>
    /// Enum to handle different report types
    /// </summary>
    public enum DQAReportType
    {
        NationalReport = 1,
        NationalReport1 = 2,
        NationalReport2 = 3,
        NationalReport3 = 4,
        ProvinceReport = 5,
        ProvinceReport1 = 6,
        ProvinceReport2 = 7,
        ProvinceReport3 = 8,
        DistrictReport1 = 9,
        DistrictReport2 = 10,
        DistrictReport3 = 11,
        HealthFacilityReport1 = 12,
        HealthFacilityReport2 = 13,
        HealthFacilityReport3 = 14
    }

    /// <summary>
    /// Represents DQA desk level output view type
    /// </summary>
    public enum DQAChartType
    {
        TabWithTableChart = 1,
        MultipleTabsWithTableChart = 2,
        SidebarTabsWithTableGraph = 3
    }

    /// <summary>
    /// Enum to handle different desk quality analysis indicators for generating report
    /// </summary>
    public enum DQAIndicatorReport
    {
        [TranslationKey("DataQualityAnalysis.1_2_1_CompletenessOfreports")]
        ReportingCompleteness = 1,

        [TranslationKey("DataQualityAnalysis.1_2_3_TimelinessOfreporting")]
        ReportingTimeliness = 2,

        [TranslationKey("DataQualityAnalysis.1_2_7_CompletenessOfCoreVariablesWithinReports")]
        ReportingVariableCompleteness = 3,

        [TranslationKey("DataQualityAnalysis.1_2_8_ConsistencyBetweenCoreVariables")]
        ReportingConsistencyBtwVariables = 4,

        [TranslationKey("DataQualityAnalysis.1_2_9_ConsistencyOverTimeForCoreIndicators")]
        ReportingConsistencyOverTime = 5,

        [TranslationKey("DataQualityAnalysis.1_2_10_ConcordanceOfKeyVariablesBetweenTwoReportingSystems")]
        ReportingConcordance = 6
    }

    /// <summary>
    /// Enum to handle different desk quality analysis variables
    /// </summary>
    public enum DQAVariables
    {
        //core variables
        TotalMalariaCases = 1,
        ConfirmMalariaCases = 2,
        MicroscopyTested = 3,
        RDTTested = 4,
        MicroscopyPositive = 5,
        RDTPositive = 6,
        AllCauseOutpatients = 7,
        AllCauseInpatients = 8,
        AllCauseDeaths = 9,
        MalariaInpatients = 10,
        MalariaInpatientDeaths = 11,
        ConfirmedMalariaCasesTreated = 12,

        //optional variables
        SuspectedMalariaCases = 13,
        PresumedMalariaCases = 14,
        IPTp = 15,
        ANC = 16
    }

    /// <summary>
    /// Enum to handle different desk quality analysis indicator to check consistency over time
    /// </summary>
    public enum ConsistencyOverTimeKeyIndicator
    {
        [TranslationKey("DataQualityAnalysis.1_ProportionOfMalariaOutpatients")]
        ProportionOfMalariaOutpatients = 1,

        [TranslationKey("DataQualityAnalysis.2_ProportionOfMalariaInpatients")]
        ProportionOfMalariaInpatients = 2,

        [TranslationKey("DataQualityAnalysis.3_ProportionOfMalariaInpatientDeaths")]
        ProportionOfMalariaInpatientDeaths = 3,

        [TranslationKey("DataQualityAnalysis.4_TestPositivityRate")]
        TestPositivityRate = 4,

        [TranslationKey("DataQualityAnalysis.5_SlidePositivityRate")]
        SlidePositivityRate = 5,

        [TranslationKey("DataQualityAnalysis.6_RDTPositivityRate")]
        RDTPositivityRate = 6,

        [TranslationKey("DataQualityAnalysis.7_ProportionOfSuspectsTested")]
        ProportionOfSuspectsTested = 7,
    }

    /// <summary>
    /// Enum to handle different desk quality analysis consistency between variables
    /// </summary>
    public enum ConsistencyBetweenVariables
    {
        // core checks
        RDTTested_RDTPositive = 1,
        MicroscopyTested_MicroscopyPositive = 2,
        AllCauseOutpatients_TotalMalariaCases = 3,
        AllCauseInpatients_MalariaInpatients = 4,
        AllCauseDeaths_MalariaInpatientDeaths = 5,
        // optional check
        ConfirmMalariaCases_ConfirmedMalariaCasesTreated = 6,
        Suspectedcases_RDTTested_RDTPositive = 7,
    }

    /// <summary>
    /// Enum to handle different health facility type
    /// </summary>
    public enum HealthFacilityType
    {
        [TranslationKey("Common.Public")]
        Public = 1,
        [TranslationKey("Common.Private")]
        Private = 2,
        [TranslationKey("Common.Community")]
        Community = 3
    }

    /// <summary>
    /// DQA priority variables enum
    /// </summary>
    public enum DQAPriorityVariables
    {
        TotalMalariaCases = 1,
        ConfirmMalariaCases = 2,
        MicroscopyTested = 3,
        RDTTested = 4,
        MicroscopyPositive = 5,
        RDTPositive = 6,
        AllCauseOutpatients = 7,
        AllCauseInpatients = 8,
        AllCauseDeaths = 9,
        MalariaInpatients = 10,
        MalariaInpatientDeaths = 11
    }

    /// <summary>
    /// DQA optional variables enum
    /// </summary>
    public enum DQAOptionalVariables
    {
        SuspectedMalariaCases = 1,
        PresumedMalariaCases = 2,
        IPTp = 3,
        ANC = 4,
        ConfirmedMalariaCasesTreated = 5,
    }

    /// <summary>
    /// Contains shell table calculation result types
    /// </summary>
    public enum ShellTableQuestionCalculationResultTypes
    {
        Percentage = 1,
        Average = 2
    }

    /// <summary>
    /// Contains geo graphic levels
    /// </summary>
    public enum GeoGraphicLevels
    {
        National = 1,
        Regional = 2,
        District = 3
    }

    /// <summary>
    /// Contains Data Analysis Report Types
    /// </summary>
    public enum DataAnalysisReports
    {
        FinalReport = 1,
        TechnicalBrief = 2,
        FinalDebriefPresentation = 3
    }

    /// <summary>
    /// Contains dqa variable applicable for
    /// </summary>
    public enum DQAVariableApplicableFor
    {
        ServiceLevel = 1,
        DeskLevel = 2,
        Both = 3
    }

    public enum DeskLevelGraphTypes
    {
        Type = 1,
        Type1 = 2,
        Type2 = 3,
        Type3 = 4
    }

    public enum OptionOrders
    {
        OptionOrder_1 = 1,
        OptionOrder_2 = 2,
        OptionOrder_3 = 3,
        OptionOrder_4 = 4
    }
}
